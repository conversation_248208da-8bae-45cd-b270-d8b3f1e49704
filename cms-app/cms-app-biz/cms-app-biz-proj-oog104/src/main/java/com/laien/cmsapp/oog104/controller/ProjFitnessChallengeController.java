package com.laien.cmsapp.oog104.controller;

import com.laien.cmsapp.bo.ProjPublishCurrentVersionInfoBO;
import com.laien.cmsapp.oog104.request.ProjFitnessChallengeListReq;
import com.laien.cmsapp.oog104.response.ProjFitnessChallengeVO;
import com.laien.cmsapp.oog104.service.IProjFitnessChallengeService;
import com.laien.cmsapp.util.RequestContextAppUtils;
import com.laien.common.controller.ResponseController;
import com.laien.common.response.setting.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * Fitness Challenge 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/15
 */
@Api(tags = "app端：Fitness Challenge")
@RestController
@RequestMapping("/{appCode}/fitnessChallenge")
public class ProjFitnessChallengeController extends ResponseController {

    @Resource
    private IProjFitnessChallengeService challengeService;

    @ApiOperation(value = "Challenge列表")
    @PostMapping("/v1/list")
    public ResponseResult<List<ProjFitnessChallengeVO>> list(@Valid @RequestBody ProjFitnessChallengeListReq req) {
        ProjPublishCurrentVersionInfoBO versionInfoBO = RequestContextAppUtils.getPublishCurrentVersionInfo();
        List<ProjFitnessChallengeVO> result = challengeService.list(versionInfoBO, req);
        return succ(result);
    }
}
